from typing import List, Dict
import json
from pathlib import Path

def to_json(findings: List[Dict]) -> str:
    return json.dumps({"findings": findings}, indent=2)

def to_markdown(findings: List[Dict]) -> str:
    if not findings:
        return "✅ No standards violations found."
    lines = ["## Standards Report"]
    for f in findings:
        lines.append(f"- **{f['severity'].upper()}** `{f['rule_id']}` at line {f.get('lineno','?')}: {f['message']}")
        if f.get("suggested_patch"):
            lines.append("  ```diff\n" + f["suggested_patch"] + "\n  ```")
    return "\n".join(lines)

def to_sarif(findings: List[Dict]) -> dict:
    # minimal SARIF v2.1.0
    return {
      "version": "2.1.0",
      "runs": [{
        "tool": {"driver": {"name": "standards-agent", "informationUri": "https://example.local"}},
        "results": [
          {
            "ruleId": f["rule_id"],
            "level": {"minor":"note","major":"warning","critical":"error"}[f["severity"]],
            "message": {"text": f["message"]},
            "locations": [{
              "physicalLocation": {
                "artifactLocation": {"uri": f.get("file","unknown.py")},
                "region": {"startLine": f.get("lineno", 1)}
              }
            }]
          } for f in findings
        ]
      }]
    }

def write_reports(out_dir: Path, findings: List[Dict]):
    out_dir.mkdir(parents=True, exist_ok=True)
    (out_dir / "report.json").write_text(to_json(findings))
    (out_dir / "report.md").write_text(to_markdown(findings))
    (out_dir / "report.sarif.json").write_text(json.dumps(to_sarif(findings), indent=2))