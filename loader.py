from pathlib import Path
import yaml
from pydantic import BaseModel, field_validator
from typing import List, Literal, Optional, Dict, Any

class Category(BaseModel):
    id: str
    weight: int
    description: str

class Detection(BaseModel):
    type: Literal['ast', 'metric', 'semgrep', 'ruff']
    pattern: Optional[str] = None
    selector: Optional[str] = None
    threshold: Optional[float] = None
    rule: Optional[str] = None

class Rule(BaseModel):
    id: str
    category: str
    severity: Literal['minor', 'major', 'critical']
    rationale: str
    languages: List[str]
    detection: Detection
    guidance: Optional[Dict[str, str]] = None
    autofix: Optional[Dict[str, Any]] = None

class Standards(BaseModel):
    version: int
    categories: List[Category]
    rules: List[Rule]

    @field_validator('rules')
    @classmethod
    def unique_ids(cls, rules):
        ids = set()
        for r in rules:
            if r.id in ids:
                raise ValueError(f"Duplicate rule id: {r.id}")
            ids.add(r.id)
        return rules

def load_standards(path: Path) -> Standards:
    data = yaml.safe_load(path.read_text())
    return Standards(**data)