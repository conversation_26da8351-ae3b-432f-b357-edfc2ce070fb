from pathlib import Path
from typing import List, Dict
from .loader import load_standards
from .detectors.ast_rules import run_ast_rule
from .detectors.metrics import run_metric_rule
from .detectors.semgrep_runner import run_semgrep
from .reviewer import llm_review
from .report import write_reports

def analyze_file(path: Path, standards_path: Path) -> List[Dict]:
    code = path.read_text(encoding="utf-8", errors="ignore")
    std = load_standards(standards_path)
    cats = {c.id: c.weight for c in std.categories}

    findings = []
    for rule in std.rules:
        if "python" not in rule.languages or path.suffix != ".py":
            continue
        det = rule.detection
        if det.type == "ast":
            for h in run_ast_rule(code, det.pattern):
                findings.append({
                    "file": str(path),
                    "lineno": h["lineno"],
                    "rule_id": rule.id,
                    "category": rule.category,
                    "severity": rule.severity,
                    "message": rule.rationale
                })
        elif det.type == "metric":
            for h in run_metric_rule(code, det.selector, det.threshold):
                findings.append({
                    "file": str(path),
                    "lineno": h["lineno"],
                    "rule_id": rule.id,
                    "category": rule.category,
                    "severity": rule.severity,
                    "message": h["detail"]
                })
        elif det.type == "semgrep":
            for h in run_semgrep(code, det.rule, filename=path.name):
                findings.append({
                    "file": str(path),


                    "lineno": h["lineno"],
                    "rule_id": rule.id,
                    "category": rule.category,
                    "severity": rule.severity,
                    "message": h["detail"]
                })
        # ruff could be handled similarly

    # Optional LLM pass (keep it targeted to changed files/chunks)
    # llm_output = llm_review(code, [r.model_dump() for r in std.rules if 'python' in r.languages])
    # findings.extend(postprocess_llm(llm_output))

    return findings

def main(paths: List[str], out_dir: str = "standards_report"):
    std_path = Path("standards.yaml")
    all_findings = []
    for p in paths:
        path = Path(p)
        if path.is_file():
            all_findings.extend(analyze_file(path, std_path))
        else:
            for f in path.rglob("*.py"):
                all_findings.extend(analyze_file(f, std_path))
    write_reports(Path(out_dir), all_findings)

if __name__ == "__main__":
    import sys
    main(sys.argv[1:] or ["."], out_dir="standards_report")