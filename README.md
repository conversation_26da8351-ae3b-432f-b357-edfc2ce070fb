Awesome idea. Here’s a tight blueprint + working scaffolds you can drop into a repo today to build a “Code Standards Agent” that reviews diffs, compares them to your org’s standards, and produces actionable feedback.

What you’ll build (quick overview)
	•	A standards spec (standards.yaml) that’s readable by humans and machines.
	•	A checker that runs static rules (AST/Semgrep/Ruff/etc.).
	•	An LLM reviewer that handles nuanced, non-lintable standards and explains trade‑offs.
	•	A scorer that assigns per-file and overall grades with weights by category (security > performance > style).
	•	Outputs as SARIF + Markdown for PR comments, plus JSON for dashboards.
	•	Hooks for pre-commit and CI (GitHub Actions).

⸻

1) Standards spec (single source of truth)

Create standards.yaml so anyone can add or tune rules without touching code.

# standards.yaml
version: 1
categories:
  - id: security
    weight: 4
    description: "Security & secrets hygiene"
  - id: reliability
    weight: 3
    description: "Resilience, error handling, timeouts, retries"
  - id: performance
    weight: 2
    description: "Complexity, allocations, hot paths"
  - id: readability
    weight: 1
    description: "Naming, comments, structure"

rules:
  - id: py.no-print-in-lib
    category: readability
    severity: minor
    rationale: "Libraries should use logging, not print."
    languages: [python]
    detection:
      type: ast
      pattern: "Call(func=Name(id='print'))"
    autofix:
      hint: "Use logging.getLogger(__name__).info/debug(...)"
      enabled: false

  - id: py.http-timeouts-required
    category: reliability
    severity: major
    rationale: "All outbound HTTP must set timeouts."
    languages: [python]
    detection:
      type: semgrep
      rule: |
        rules:
          - id: requests-no-timeout
            languages: [python]
            severity: ERROR
            pattern-either:
              - pattern: requests.$FUNC(..., timeout=None)
              - pattern: requests.$FUNC(..., timeout=$X) # ensure $X exists
            # we’ll post-process to detect missing timeout
    guidance:
      good: "requests.get(url, timeout=5)"
      bad:  "requests.get(url)"

  - id: py.function-length
    category: readability
    severity: minor
    rationale: "Functions > 50 lines harm maintainability."
    languages: [python]
    detection:
      type: metric
      selector: "function_length"
      threshold: 50
    guidance:
      good: "Extract helpers, keep functions focused."


⸻

2) Minimal Python project layout

standards-agent/
├─ standards.yaml
├─ agent/
│  ├─ __init__.py
│  ├─ loader.py          # loads & validates standards
│  ├─ detectors/
│  │  ├─ ast_rules.py    # simple AST walkers
│  │  ├─ metrics.py      # function length, cyclomatic complexity
│  │  ├─ semgrep_runner.py
│  │  └─ ruff_runner.py
│  ├─ reviewer.py        # LLM pass for nuanced checks
│  ├─ score.py           # weighted scoring
│  ├─ report.py          # JSON, Markdown, SARIF
│  └─ run.py             # CLI entrypoint
├─ pyproject.toml
└─ .pre-commit-config.yaml


⸻

3) Core: load standards + run detectors

agent/loader.py

from pathlib import Path
import yaml
from pydantic import BaseModel, field_validator
from typing import List, Literal, Optional, Dict, Any

class Category(BaseModel):
    id: str
    weight: int
    description: str

class Detection(BaseModel):
    type: Literal['ast', 'metric', 'semgrep', 'ruff']
    pattern: Optional[str] = None
    selector: Optional[str] = None
    threshold: Optional[float] = None
    rule: Optional[str] = None

class Rule(BaseModel):
    id: str
    category: str
    severity: Literal['minor', 'major', 'critical']
    rationale: str
    languages: List[str]
    detection: Detection
    guidance: Optional[Dict[str, str]] = None
    autofix: Optional[Dict[str, Any]] = None

class Standards(BaseModel):
    version: int
    categories: List[Category]
    rules: List[Rule]

    @field_validator('rules')
    @classmethod
    def unique_ids(cls, rules):
        ids = set()
        for r in rules:
            if r.id in ids:
                raise ValueError(f"Duplicate rule id: {r.id}")
            ids.add(r.id)
        return rules

def load_standards(path: Path) -> Standards:
    data = yaml.safe_load(path.read_text())
    return Standards(**data)

agent/detectors/ast_rules.py (example AST detector)

import ast
from typing import List, Dict, Any

class PrintCallVisitor(ast.NodeVisitor):
    def __init__(self):
        self.hits = []

    def visit_Call(self, node):
        if isinstance(node.func, ast.Name) and node.func.id == 'print':
            self.hits.append({"lineno": node.lineno, "col": node.col_offset})
        self.generic_visit(node)

AST_PATTERNS = {
    "Call(func=Name(id='print'))": PrintCallVisitor,
}

def run_ast_rule(code: str, pattern: str) -> List[Dict[str, Any]]:
    tree = ast.parse(code)
    visitor_cls = AST_PATTERNS.get(pattern)
    if not visitor_cls:
        return []
    v = visitor_cls()
    v.visit(tree)
    return v.hits

agent/detectors/metrics.py (function length)

import ast
from typing import List, Dict, Any

def function_lengths(code: str) -> List[Dict[str, Any]]:
    tree = ast.parse(code)
    results = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            # crude: last line - first line + 1
            length = (node.end_lineno or node.lineno) - node.lineno + 1
            results.append({"name": node.name, "lineno": node.lineno, "length": length})
    return results

def run_metric_rule(code: str, selector: str, threshold: float):
    if selector == "function_length":
        hits = []
        for f in function_lengths(code):
            if f["length"] > threshold:
                hits.append({
                    "lineno": f["lineno"],
                    "detail": f"{f['name']} length {f['length']} > {threshold}"
                })
        return hits
    return []

Optional: Semgrep & Ruff

You can shell out to Semgrep/Ruff (or call their Python APIs) to reuse mature rulesets.

# agent/detectors/semgrep_runner.py
import json, subprocess, tempfile, pathlib
from typing import List, Dict, Any

def run_semgrep(code: str, inline_rule_yaml: str, filename: str = "tmp.py") -> List[Dict[str, Any]]:
    with tempfile.TemporaryDirectory() as td:
        p = pathlib.Path(td)
        (p / filename).write_text(code)
        (p / "rule.yaml").write_text(inline_rule_yaml)
        cmd = ["semgrep", "--config", str(p / "rule.yaml"), str(p / filename), "--json"]
        out = subprocess.run(cmd, capture_output=True, text=True, check=False)
        try:
            data = json.loads(out.stdout or "{}")
        except json.JSONDecodeError:
            return []
        results = []
        for r in data.get("results", []):
            results.append({
                "lineno": r["start"]["line"],
                "detail": r["extra"]["message"],
                "check_id": r.get("check_id")
            })
        return results


⸻

4) LLM reviewer for nuance

Use an LLM to evaluate non-binary standards (e.g., architecture boundaries, error handling quality, naming consistency). It should:
	•	Read the diff or file chunk.
	•	Read relevant rules from standards.yaml (filter by language/category).
	•	Emit structured JSON: findings, rationales, and suggested patches.

agent/reviewer.py (model-agnostic stub)

from typing import List, Dict, Any

SYSTEM_PROMPT = """You are a senior code reviewer enforcing the team's standards.
Evaluate code against the provided rules. Be concise, specific, and actionable.
Return ONLY JSON that matches the provided schema."""

SCHEMA = {
  "type": "object",
  "properties": {
    "findings": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "rule_id": {"type": "string"},
          "severity": {"type": "string", "enum": ["minor","major","critical"]},
          "line": {"type": "integer"},
          "message": {"type": "string"},
          "suggested_patch": {"type": "string"}
        },
        "required": ["rule_id","severity","message"]
      }
    }
  },
  "required": ["findings"]
}

def llm_review(code: str, rules: List[Dict[str, Any]]) -> Dict[str, Any]:
    # Pseudocode to call your provider of choice; ensure JSON-mode / function call
    # Replace with your SDK call; pass SYSTEM_PROMPT, rules (trimmed), and code.
    # return client.responses.parse_json(...)
    return {"findings": []}


⸻

5) Scoring model

agent/score.py

from typing import List, Dict

SEV_POINTS = {"minor": 1, "major": 3, "critical": 6}

def compute_score(findings: List[Dict], categories_weights: Dict[str, int]):
    # sum(weight * severity_points) per finding; convert to 0..100 grade
    total_penalty = 0
    for f in findings:
        w = categories_weights.get(f["category"], 1)
        total_penalty += w * SEV_POINTS.get(f["severity"], 1)
    # simple normalization; tweak to your taste
    score = max(0, 100 - min(100, total_penalty))
    return score


⸻

6) Reports: JSON, Markdown, SARIF

agent/report.py

from typing import List, Dict
import json
from pathlib import Path

def to_json(findings: List[Dict]) -> str:
    return json.dumps({"findings": findings}, indent=2)

def to_markdown(findings: List[Dict]) -> str:
    if not findings:
        return "✅ No standards violations found."
    lines = ["## Standards Report"]
    for f in findings:
        lines.append(f"- **{f['severity'].upper()}** `{f['rule_id']}` at line {f.get('lineno','?')}: {f['message']}")
        if f.get("suggested_patch"):
            lines.append("  ```diff\n" + f["suggested_patch"] + "\n  ```")
    return "\n".join(lines)

def to_sarif(findings: List[Dict]) -> dict:
    # minimal SARIF v2.1.0
    return {
      "version": "2.1.0",
      "runs": [{
        "tool": {"driver": {"name": "standards-agent", "informationUri": "https://example.local"}},
        "results": [
          {
            "ruleId": f["rule_id"],
            "level": {"minor":"note","major":"warning","critical":"error"}[f["severity"]],
            "message": {"text": f["message"]},
            "locations": [{
              "physicalLocation": {
                "artifactLocation": {"uri": f.get("file","unknown.py")},
                "region": {"startLine": f.get("lineno", 1)}
              }
            }]
          } for f in findings
        ]
      }]
    }

def write_reports(out_dir: Path, findings: List[Dict]):
    out_dir.mkdir(parents=True, exist_ok=True)
    (out_dir / "report.json").write_text(to_json(findings))
    (out_dir / "report.md").write_text(to_markdown(findings))
    (out_dir / "report.sarif.json").write_text(json.dumps(to_sarif(findings), indent=2))


⸻

7) Runner that ties it together

agent/run.py

from pathlib import Path
from typing import List, Dict
from .loader import load_standards
from .detectors.ast_rules import run_ast_rule
from .detectors.metrics import run_metric_rule
from .detectors.semgrep_runner import run_semgrep
from .reviewer import llm_review
from .report import write_reports

def analyze_file(path: Path, standards_path: Path) -> List[Dict]:
    code = path.read_text(encoding="utf-8", errors="ignore")
    std = load_standards(standards_path)
    cats = {c.id: c.weight for c in std.categories}

    findings = []
    for rule in std.rules:
        if "python" not in rule.languages or path.suffix != ".py":
            continue
        det = rule.detection
        if det.type == "ast":
            for h in run_ast_rule(code, det.pattern):
                findings.append({
                    "file": str(path),
                    "lineno": h["lineno"],
                    "rule_id": rule.id,
                    "category": rule.category,
                    "severity": rule.severity,
                    "message": rule.rationale
                })
        elif det.type == "metric":
            for h in run_metric_rule(code, det.selector, det.threshold):
                findings.append({
                    "file": str(path),
                    "lineno": h["lineno"],
                    "rule_id": rule.id,
                    "category": rule.category,
                    "severity": rule.severity,
                    "message": h["detail"]
                })
        elif det.type == "semgrep":
            for h in run_semgrep(code, det.rule, filename=path.name):
                findings.append({
                    "file": str(path),
                    "lineno": h["lineno"],
                    "rule_id": rule.id,
                    "category": rule.category,
                    "severity": rule.severity,
                    "message": h["detail"]
                })
        # ruff could be handled similarly

    # Optional LLM pass (keep it targeted to changed files/chunks)
    # llm_output = llm_review(code, [r.model_dump() for r in std.rules if 'python' in r.languages])
    # findings.extend(postprocess_llm(llm_output))

    return findings

def main(paths: List[str], out_dir: str = "standards_report"):
    std_path = Path("standards.yaml")
    all_findings = []
    for p in paths:
        path = Path(p)
        if path.is_file():
            all_findings.extend(analyze_file(path, std_path))
        else:
            for f in path.rglob("*.py"):
                all_findings.extend(analyze_file(f, std_path))
    write_reports(Path(out_dir), all_findings)

if __name__ == "__main__":
    import sys
    main(sys.argv[1:] or ["."], out_dir="standards_report")


⸻

8) Pre-commit & CI wiring

.pre-commit-config.yaml

repos:
  - repo: local
    hooks:
      - id: standards-agent
        name: standards-agent
        entry: python -m agent.run
        language: system
        pass_filenames: true
        files: \.py$

GitHub Action (PR comments + SARIF upload)

.github/workflows/standards.yml

name: standards
on:
  pull_request:
    types: [opened, synchronize, reopened]
jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with: { python-version: "3.12" }
      - run: pip install semgrep ruff pyyaml pydantic
      - run: python -m agent.run . --out_dir standards_report
      - uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: standards_report/report.sarif.json
      - name: Post PR comment
        if: always()
        run: |
          BODY="$(cat standards_report/report.md)"
          gh pr comment "$PR_NUMBER" --body "$BODY"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ github.event.pull_request.number }}


⸻

9) What the agent checks well vs. uses the LLM for

Great for static checks (fast & deterministic):
	•	“No print in library code”
	•	“HTTP calls require timeout”
	•	“Function length / complexity thresholds”
	•	“Disallow wildcard imports, eval/exec, bare except:”
	•	“Module boundaries (via path globs)”

LLM is best for:
	•	“Is error handling meaningful and consistent?”
	•	“Is naming intent-revealing?”
	•	“Is this test readable and focused?”
	•	“Does this abstraction leak?”
	•	“Is logging level appropriate?”

Use the static pass to flag candidates; use the LLM pass sparingly on changed hunks to keep it cheap and fast.

⸻

10) Extending beyond Python
	•	Swap/augment detectors for TypeScript/Go/Java using corresponding AST libraries or Semgrep rules.
	•	Keep standards.yaml language-agnostic: add languages: [typescript], etc.
	•	Normalize all findings to the same JSON schema → one scoring/report pipeline.

⸻

11) Practical adoption tips
	•	Start with 10–15 high-impact rules (security/reliability first).
	•	Mark some as advisory (severity: minor) for the first few weeks to build goodwill.
	•	Publish a sandbox command (make standards) devs can run locally.
	•	Track trendlines (average score by repo/team) in your dashboard; reward improvements.

⸻

If you want, tell me your target stack (Python/TS/etc.) and 5–10 “must-have” standards, and I’ll generate a first standards.yaml + detectors tailored to your codebase.