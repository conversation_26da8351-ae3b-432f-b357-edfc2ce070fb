from typing import List, Dict, Any

SYSTEM_PROMPT = """You are a senior code reviewer enforcing the team's standards.
Evaluate code against the provided rules. Be concise, specific, and actionable.
Return ONLY JSON that matches the provided schema."""

SCHEMA = {
  "type": "object",
  "properties": {
    "findings": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "rule_id": {"type": "string"},
          "severity": {"type": "string", "enum": ["minor","major","critical"]},
          "line": {"type": "integer"},
          "message": {"type": "string"},
          "suggested_patch": {"type": "string"}
        },
        "required": ["rule_id","severity","message"]
      }
    }
  },
  "required": ["findings"]
}

def llm_review(code: str, rules: List[Dict[str, Any]]) -> Dict[str, Any]:
    # Pseudocode to call your provider of choice; ensure JSON-mode / function call
    # Replace with your SDK call; pass SYSTEM_PROMPT, rules (trimmed), and code.
    # return client.responses.parse_json(...)
    return {"findings": []}