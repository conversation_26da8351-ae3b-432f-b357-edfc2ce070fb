import ast
from typing import List, Dict, Any

def function_lengths(code: str) -> List[Dict[str, Any]]:
    tree = ast.parse(code)
    results = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            # crude: last line - first line + 1
            length = (node.end_lineno or node.lineno) - node.lineno + 1
            results.append({"name": node.name, "lineno": node.lineno, "length": length})
    return results

def run_metric_rule(code: str, selector: str, threshold: float):
    if selector == "function_length":
        hits = []
        for f in function_lengths(code):
            if f["length"] > threshold:
                hits.append({
                    "lineno": f["lineno"],
                    "detail": f"{f['name']} length {f['length']} > {threshold}"
                })
        return hits
    return []