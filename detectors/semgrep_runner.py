# agent/detectors/semgrep_runner.py
import json, subprocess, tempfile, pathlib
from typing import List, Dict, Any

def run_semgrep(code: str, inline_rule_yaml: str, filename: str = "tmp.py") -> List[Dict[str, Any]]:
    with tempfile.TemporaryDirectory() as td:
        p = pathlib.Path(td)
        (p / filename).write_text(code)
        (p / "rule.yaml").write_text(inline_rule_yaml)
        cmd = ["semgrep", "--config", str(p / "rule.yaml"), str(p / filename), "--json"]
        out = subprocess.run(cmd, capture_output=True, text=True, check=False)
        try:
            data = json.loads(out.stdout or "{}")
        except json.JSONDecodeError:
            return []
        results = []
        for r in data.get("results", []):
            results.append({
                "lineno": r["start"]["line"],
                "detail": r["extra"]["message"],
                "check_id": r.get("check_id")
            })
        return results