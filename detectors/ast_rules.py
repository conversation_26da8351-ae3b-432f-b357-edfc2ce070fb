import ast
from typing import List, Dict, Any

class PrintCallVisitor(ast.NodeVisitor):
    def __init__(self):
        self.hits = []

    def visit_Call(self, node):
        if isinstance(node.func, ast.Name) and node.func.id == 'print':
            self.hits.append({"lineno": node.lineno, "col": node.col_offset})
        self.generic_visit(node)

AST_PATTERNS = {
    "Call(func=Name(id='print'))": PrintCallVisitor,
}

def run_ast_rule(code: str, pattern: str) -> List[Dict[str, Any]]:
    tree = ast.parse(code)
    visitor_cls = AST_PATTERNS.get(pattern)
    if not visitor_cls:
        return []
    v = visitor_cls()
    v.visit(tree)
    return v.hits