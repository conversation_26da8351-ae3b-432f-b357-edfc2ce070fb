# standards.yaml
version: 1
categories:
  - id: security
    weight: 4
    description: "Security & secrets hygiene"
  - id: reliability
    weight: 3
    description: "Resilience, error handling, timeouts, retries"
  - id: performance
    weight: 2
    description: "Complexity, allocations, hot paths"
  - id: readability
    weight: 1
    description: "Naming, comments, structure"

rules:
  - id: py.no-print-in-lib
    category: readability
    severity: minor
    rationale: "Libraries should use logging, not print."
    languages: [python]
    detection:
      type: ast
      pattern: "Call(func=Name(id='print'))"
    autofix:
      hint: "Use logging.getLogger(__name__).info/debug(...)"
      enabled: false

  - id: py.http-timeouts-required
    category: reliability
    severity: major
    rationale: "All outbound HTTP must set timeouts."
    languages: [python]
    detection:
      type: semgrep
      rule: |
        rules:
          - id: requests-no-timeout
            languages: [python]
            severity: ERROR
            pattern-either:
              - pattern: requests.$FUNC(..., timeout=None)
              - pattern: requests.$FUNC(..., timeout=$X) # ensure $X exists
            # we’ll post-process to detect missing timeout
    guidance:
      good: "requests.get(url, timeout=5)"
      bad:  "requests.get(url)"

  - id: py.function-length
    category: readability
    severity: minor
    rationale: "Functions > 50 lines harm maintainability."
    languages: [python]
    detection:
      type: metric
      selector: "function_length"
      threshold: 50
    guidance:
      good: "Extract helpers, keep functions focused."