from typing import List, Dict

SEV_POINTS = {"minor": 1, "major": 3, "critical": 6}

def compute_score(findings: List[Dict], categories_weights: Dict[str, int]):
    # sum(weight * severity_points) per finding; convert to 0..100 grade
    total_penalty = 0
    for f in findings:
        w = categories_weights.get(f["category"], 1)
        total_penalty += w * SEV_POINTS.get(f["severity"], 1)
    # simple normalization; tweak to your taste
    score = max(0, 100 - min(100, total_penalty))
    return score