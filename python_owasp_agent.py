#!/usr/bin/env python3
"""
pilot_owasp_agent.py
A minimal OWASP-style checker using only:
- Python stdlib + regex patterns (no external scanners)
- OpenAI SDK (optional) for actionable suggestions

Runs on changed files (git diff) or entire repo.
Outputs Markdown. In CI, can post a PR comment if env has GITHUB_TOKEN + PR info.

Env (optional):
  OPENAI_API_KEY         -> enable LLM suggestions
  GITHUB_TOKEN           -> post PR comment (if PR context available)
  GITHUB_REPOSITORY      -> e.g. owner/repo
  GITHUB_EVENT_NAME      -> 'pull_request' etc.
  GITHUB_EVENT_PATH      -> path to event JSON (Actions provides this)
  FILE_GLOB              -> override to scan only this glob (e.g., "src/**.py")

Usage:
  python pilot_owasp_agent.py               # scan changed files vs main (best-effort)
  python pilot_owasp_agent.py --all         # scan all files in repo
  python pilot_owasp_agent.py --base main   # diff against a base branch
"""

import os, re, sys, json, subprocess, argparse, textwrap
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import List, Dict, Optional, Tuple

# ---------- Config ----------
ROOT = Path(__file__).resolve().parent
STANDARDS = ROOT / ".repo-standards" / "security.yml"  # optional: agent will cite if present

# Minimal “house” rules mapped to OWASP categories (regex-based)
# Keep them small and precise for a pilot (low false positives).
REGEX_RULES = [
    # A02: Cryptographic Failures
    dict(
        id="A02-VERIFY-FALSE",
        desc="TLS verification disabled",
        severity="HIGH",
        lang=["py"],
        pattern=r"requests\.\w+\([^)]*?verify\s*=\s*False",
        hint="Enable TLS verification (verify=True) or configure proper certs.",
    ),
    dict(
        id="A02-WEAK-HASH",
        desc="Weak hash (MD5/SHA1)",
        severity="MED",
        lang=["py","js","ts"],
        pattern=r"(hashlib\.(md5|sha1)\(|crypto\.createHash\(['\"](md5|sha1)['\"]\))",
        hint="Use SHA-256+ for general hashing; bcrypt/scrypt/Argon2 for passwords.",
    ),
    # A03: Injection
    dict(
        id="A03-SQLI-CONCAT",
        desc="String-built SQL query",
        severity="HIGH",
        lang=["py"],
        pattern=r"cursor\.execute\([^)]*(%s|\+|f['\"])",
        hint="Use parameterized queries, never string concatenation for SQL.",
    ),
    dict(
        id="A03-SHELL-TRUE",
        desc="subprocess with shell=True",
        severity="HIGH",
        lang=["py"],
        pattern=r"subprocess\.\w+\([^)]*shell\s*=\s*True",
        hint="Use argument lists (no shell) and validate inputs.",
    ),
    dict(
        id="A03-EXEC",
        desc="child_process.exec(…)",
        severity="MED",
        lang=["js","ts"],
        pattern=r"child_process\.(exec|execSync)\(",
        hint="Prefer execFile/spawn with args; sanitize inputs.",
    ),
    # A07: Identification & Authentication Failures
    dict(
        id="A07-COOKIE-FLAGS",
        desc="Cookie without secure flags",
        severity="MED",
        lang=["py","js","ts"],
        pattern=r"(response\.set_cookie|res\.cookie)\(",
        hint="Set HttpOnly, Secure, and SameSite on cookies.",
    ),
    # A10: SSRF
    dict(
        id="A10-SSRF-PY",
        desc="requests to user-controlled URL",
        severity="HIGH",
        lang=["py"],
        pattern=r"requests\.\w+\(\s*(request\.[a-zA-Z_]+\.(get|post)\(|request\[[^\]]+\])",
        hint="Validate URL against allowlist; block private/metadata ranges.",
    ),
    dict(
        id="A10-SSRF-TS",
        desc="fetch/axios to user-controlled URL",
        severity="HIGH",
        lang=["js","ts"],
        pattern=r"(fetch|axios\.\w*)\(\s*(req\.[a-zA-Z_]+|req\[[^\]]+\])",
        hint="Validate URL against allowlist; block private/metadata ranges.",
    ),
]

IGNORE_DIRS = {".git", ".venv", "venv", "node_modules", "dist", "build", ".next", "__pycache__"}

# ---------- Utilities ----------
def run(cmd: List[str], cwd: Optional[Path]=None) -> Tuple[int,str,str]:
    p = subprocess.run(cmd, cwd=cwd, text=True, capture_output=True)
    return p.returncode, p.stdout, p.stderr

def list_changed_files(base: str) -> List[str]:
    # Best effort: compare with base
    rc, out, _ = run(["git", "diff", "--name-only", base, "HEAD"])
    if rc != 0 or not out.strip():
        # fallback to last commit
        rc2, out2, _ = run(["git", "diff", "--name-only", "HEAD~1", "HEAD"])
        return [f.strip() for f in out2.splitlines() if f.strip()]
    return [f.strip() for f in out.splitlines() if f.strip()]

def list_all_files() -> List[str]:
    files = []
    for p in ROOT.rglob("*"):
        if p.is_dir() or not p.is_file():
            continue
        parts = set(p.parts)
        if parts & IGNORE_DIRS:
            continue
        files.append(str(p.relative_to(ROOT)))
    return files

def lang_of(path: str) -> str:
    if path.endswith(".py"): return "py"
    if path.endswith(".ts"): return "ts"
    if path.endswith(".tsx"): return "ts"
    if path.endswith(".js") or path.endswith(".mjs") or path.endswith(".cjs"): return "js"
    return ""

def load_security_excerpt(max_chars=8000) -> str:
    if not STANDARDS.exists(): return ""
    try:
        txt = STANDARDS.read_text(encoding="utf-8", errors="ignore")
        return txt[:max_chars]
    except Exception:
        return ""

def redact(text: str) -> str:
    # Basic secret redaction (very conservative)
    text = re.sub(r"AKIA[0-9A-Z]{16}", "[REDACTED]", text)
    text = re.sub(r"(?i)(api|secret|token)[_\-\s]*key\s*[:=]\s*['\"][A-Za-z0-9/+=\-_]{16,}['\"]", "[REDACTED]", text)
    return text

# ---------- Scanning ----------
@dataclass
class Finding:
    rule_id: str
    owasp: str
    severity: str
    file: str
    line: int
    code: str
    desc: str
    hint: str

def scan_file(path: Path, rules: List[Dict]) -> List[Finding]:
    lang = lang_of(str(path))
    if not lang: return []
    try:
        src = path.read_text(encoding="utf-8", errors="ignore")
    except Exception:
        return []
    findings: List[Finding] = []
    for r in rules:
        if lang not in r["lang"]:
            continue
        rx = re.compile(r["pattern"])
        for m in rx.finditer(src):
            line = src.count("\n", 0, m.start()) + 1
            snippet = src[max(0, m.start()-60): m.end()+60].replace("\n", "\\n")
            findings.append(Finding(
                rule_id=r["id"],
                owasp=r["id"].split("-")[0],  # A02, A03, …
                severity=r["severity"],
                file=str(path),
                line=line,
                code=snippet[:240],
                desc=r["desc"],
                hint=r["hint"],
            ))
    return findings

def scan_files(files: List[str]) -> List[Finding]:
    out: List[Finding] = []
    for f in files:
        # Skip big/binary-ish files
        p = ROOT / f
        if not p.exists() or p.is_dir(): continue
        if any(part in IGNORE_DIRS for part in p.parts): continue
        if p.suffix.lower() in {".png",".jpg",".jpeg",".gif",".pdf",".zip",".tar",".gz",".lock"}:
            continue
        out.extend(scan_file(p, REGEX_RULES))
    return out

# ---------- OpenAI Suggestions ----------
def llm_suggestions(findings: List[Finding], standards_excerpt: str, max_findings: int = 12) -> Optional[str]:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        return None
    try:
        from openai import OpenAI
        client = OpenAI(api_key=api_key)
        # Prepare a compact, redacted context
        items = []
        for f in findings[:max_findings]:
            items.append(
                f"- {f.owasp} {f.rule_id} {f.severity} @ `{f.file}:{f.line}` :: {f.desc}\n"
                f"  code: `{redact(f.code)}`"
            )
        prompt = textwrap.dedent(f"""
        You are a security reviewer. Given OWASP-style regex findings, produce concise,
        actionable suggestions. For each item:
          - Map to OWASP category (already included like A02/A03—keep it).
          - Give a one-line fix and, if safe, a minimal patch hunk.
          - If the issue is environment/config specific, say which env var or setting to change.
        If applicable, cite exact guidance from the provided standards (short quotes).

        STANDARDS (optional excerpt):
        {standards_excerpt or "(none provided)"}

        FINDINGS:
        {chr(10).join(items)}
        """)
        resp = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role":"user","content":prompt}],
            temperature=0.1,
        )
        return resp.choices[0].message.content
    except Exception as e:
        return f"_LLM suggestions unavailable: {e}_"

# ---------- GitHub comment ----------
def gh_post_comment(markdown: str) -> bool:
    token = os.getenv("GITHUB_TOKEN")
    repo = os.getenv("GITHUB_REPOSITORY")
    event_path = os.getenv("GITHUB_EVENT_PATH")
    if not (token and repo and event_path and Path(event_path).exists()):
        return False
    try:
        evt = json.loads(Path(event_path).read_text())
        if "pull_request" not in evt:
            return False
        pr_num = evt["pull_request"]["number"]
        import requests
        r = requests.post(
            f"https://api.github.com/repos/{repo}/issues/{pr_num}/comments",
            headers={"Authorization": f"Bearer {token}", "Accept": "application/vnd.github+json"},
            json={"body": markdown},
            timeout=30,
        )
        return r.status_code < 300
    except Exception:
        return False

# ---------- Report ----------
def render_report(findings: List[Finding], llm_md: Optional[str]) -> str:
    if not findings:
        return "### ✅ OWASP Regex Pilot\nNo issues detected by regex rules."
    # group by OWASP
    by_cat: Dict[str, List[Finding]] = {}
    for f in findings:
        by_cat.setdefault(f.owasp, []).append(f)
    lines = ["### 🔐 OWASP Regex Pilot Findings"]
    sev_order = {"HIGH":0, "MED":1, "LOW":2}
    for cat in sorted(by_cat.keys()):
        lines.append(f"\n#### {cat}")
        for f in sorted(by_cat[cat], key=lambda x: (sev_order.get(x.severity, 9), x.file, x.line)):
            lines.append(
                f"- **{f.severity}** `{f.file}:{f.line}` **{f.rule_id}** — {f.desc}\n"
                f"  - hint: {f.hint}\n"
                f"  - code: `{redact(f.code)}`"
            )
    if llm_md:
        lines.append("\n---\n### 🤖 Suggestions\n" + llm_md)
    return "\n".join(lines)

# ---------- Main ----------
def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--all", action="store_true", help="scan entire repo")
    ap.add_argument("--base", default=os.getenv("GITHUB_BASE_REF") or "origin/main",
                    help="git base for diff (default origin/main)")
    args = ap.parse_args()

    file_glob = os.getenv("FILE_GLOB")
    if args.all:
        files = list_all_files()
    elif file_glob:
        files = [str(p) for p in ROOT.rglob(file_glob)]
    else:
        files = list_changed_files(args.base)

    if not files:
        print("No files to scan (did you commit changes?).")
        print("Tip: try --all")
        sys.exit(0)

    findings = scan_files(files)
    standards_excerpt = load_security_excerpt()
    llm_md = llm_suggestions(findings, standards_excerpt) if findings else None
    report = render_report(findings, llm_md)

    # Print for logs, and post comment in PR if possible
    print(report)
    posted = gh_post_comment(report)
    if posted:
        print("\n[posted comment to PR]")
    # Exit non-zero only on HIGH issues to keep pilot dev-friendly
    if any(f.severity == "HIGH" for f in findings):
        sys.exit(1)
    sys.exit(0)

if __name__ == "__main__":
    main()